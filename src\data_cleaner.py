import warnings
import numpy as np
from typing import Any, Callable, <PERSON>ple

from src.plotter.utils import valid_file_string
from src.data_core import DataInstance




def default_cleaner(instance: DataInstance) -> DataInstance:
    """A default cleaning function that does nothing."""
    return instance


def typo_template(key: str, correct_value: Any, possible_typos: Tuple[Any, ...]) -> Callable[[DataInstance], DataInstance]:
    """A template for creating a function to fix typos in a specific key."""
    def rule(instance: DataInstance) -> DataInstance:
        if instance[key] in possible_typos:
            warnings.warn(f"Corrected typo in '{key}' from {instance[key]} to {correct_value}.")
            return instance.with_updates(**{key: correct_value})
        return instance
    return rule

def align_template(correct_key: str, target_keys: Tuple[str, ...]) -> Callable[[DataInstance], DataInstance]:
    """A template for creating a function to align multiple keys to a single correct key."""
    def rule(instance: DataInstance) -> DataInstance:
        new_data = {}
        for k, v in instance.items():
            if k in target_keys:
                warnings.warn(f"Aligned '{k}' to '{correct_key}'.")
                new_data[correct_key] = v
            else:
                new_data[k] = v
        return DataInstance(**new_data)
    return rule


def joe_data_cleaner(instance: DataInstance) -> DataInstance:
    """A custom cleaning function for Joe's data."""

    def rule1_unify_nan_value_indicator(instance: DataInstance) -> DataInstance:
        """Unify the NaN value indicator to np.nan."""        
        NAN_KEYWORDS = (0, "0", "?", "Unknown", "Pyramidal?", "No Fill")
        new_data = {}
        for k, v in instance.items():
            if v in NAN_KEYWORDS:
                new_data[k] = np.nan
                warnings.warn(f"Replaced {v} with np.nan for key {k}")
            else:
                new_data[k] = v
        return DataInstance(**new_data)
    
    def rule2_validate_keys(instance: DataInstance) -> DataInstance:
        """Validate the keys in the instance."""
        new_data = {}
        for k, v in instance.items():
            valid_k = valid_file_string(k)
            if valid_k != k:
                warnings.warn(f"Replaced key '{k}' with '{valid_k}'.")
                new_data[valid_k] = v
            else:
                new_data[k] = v
        return DataInstance(**new_data)
    
    funcs = [
        rule1_unify_nan_value_indicator, 
        rule2_validate_keys,
        typo_template('Morphology', 'Pyramidal', ("Pryamidal", )), 
        typo_template('GrpTrain', 'PSEcntl', ("PSECntl", )),
        typo_template('Training', 'PSEcntl24 + PSE24', ("PCntl24 + PSE24", "PSECntl24 + PSE24",)),
        typo_template('Training', 'PSEcntl48 + PSE24', ("PCntl48 + PSE24", )),
        typo_template('Training', 'PSEcntl72 + PSE24', ("PCntl72 + PSE24", )),
        typo_template('Training', 'ACC24 + SAT24', ("ACC24 +SAT24", )),
        align_template('qEPSC (pA)(0-3ms Eve', ('qEPSC', )),
        align_template('Depth (μm)', ('Depth', )),
        align_template('Blank (Hz)(Last 20%)', ('Blank (Last 20%)', )),
        align_template('Performance(Last 20%', ('Performance (Last 20', )),
        align_template('Stim (Hz)(Last 20%)', ('Stim (Last 20%)', )),
    ]
    for func in funcs:
        instance = func(instance)
    return instance