from typing import Optional, Dict, Any
import matplotlib.pyplot as plt
import matplotlib.cm as cm
import numpy as np
import pandas as pd
from scipy import stats
import os

from src.plotter.utils import valid_file_string
from src.data_core import Dataset 

# Helper function for data preparation
def _prepare_dataframe(dataset: Dataset, x_key: str, y_key: str, color_key: Optional[str]) -> pd.DataFrame:
    """Extracts and cleans data into a pandas DataFrame."""
    data: Dict[str, Any] = {
        'x': dataset.get_param_list(x_key),
        'y': dataset.get_param_list(y_key),
    }
    if color_key:
        data['color'] = dataset.get_param_list(color_key)

    df = pd.DataFrame(data)

    # Convert x and y to numeric, coercing errors to NaN
    df['x'] = pd.to_numeric(df['x'], errors='coerce')
    df['y'] = pd.to_numeric(df['y'], errors='coerce')

    # Drop rows where x or y are NaN
    df.dropna(subset=['x', 'y'], inplace=True)

    if not len(df):
        raise ValueError(f"No valid numeric data points found for '{x_key}' and '{y_key}'.")

    # Try converting color column to numeric if it exists
    if color_key:
        df['color_numeric'] = pd.to_numeric(df['color'], errors='coerce')

    return df

# Helper function for plotting scatter points
def _draw_scatter_points(ax: plt.Axes, df: pd.DataFrame, color_key: Optional[str]) -> None:
    """Handles the logic for drawing scatter points with or without color encoding."""
    # Case 1: No color key provided
    if not color_key:
        ax.scatter(df['x'], df['y'], alpha=0.7)
        return

    # Case 2: Color key is numeric
    is_numeric_color = pd.api.types.is_numeric_dtype(df['color'])
    if is_numeric_color:
        valid_color_df = df.dropna(subset=['color_numeric'])
        nan_color_df = df[df['color_numeric'].isna()]
        
        # Plot points with valid numeric color
        scatter = ax.scatter(
            valid_color_df['x'], valid_color_df['y'], 
            c=valid_color_df['color_numeric'], cmap='viridis', alpha=0.7
        )
        plt.colorbar(scatter, ax=ax, label=color_key)
        
        # Plot points with missing color data in gray
        if not nan_color_df.empty:
            ax.scatter(nan_color_df['x'], nan_color_df['y'], c='gray', alpha=0.5, label='Missing color data')

    # Case 3: Color key is categorical
    else:
        categories = df['color'].dropna().unique()
        colors = cm.get_cmap('tab10')((np.arange(len(categories))%10)/10)
        color_map = dict(zip(categories, colors))

        for category, color in color_map.items():
            subset = df[df['color'] == category]
            ax.scatter(subset['x'], subset['y'], c=[color], label=str(category), alpha=0.7)
        
        nan_color_df = df[df['color'].isna()]
        if not nan_color_df.empty:
            ax.scatter(nan_color_df['x'], nan_color_df['y'], c='gray', alpha=0.5, label='Missing color data')

# Helper function for statistics and best-fit line
def _add_stats_and_fit_line(ax: plt.Axes, df: pd.DataFrame, test: str) -> float:
    """Calculates correlation, adds a text box, and plots a line of best fit."""
    correlation_funcs = {
        'pearson': stats.pearsonr,
        'spearman': stats.spearmanr
    }
    
    test_func = correlation_funcs.get(test.lower())
    if not test_func:
        raise ValueError(f"Unsupported test: {test}. Use 'pearson' or 'spearman'.")

    r_value, p_value = test_func(df['x'], df['y'])
    
    # Display correlation results on the plot
    textstr = f'{test.capitalize()} r = {r_value:.3f}\np-value = {p_value:.3f}'
    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.05, 0.95, textstr, transform=ax.transAxes, fontsize=12,
            verticalalignment='top', bbox=props)

    # Plot line of best fit
    slope, intercept, _, _, _ = stats.linregress(df['x'], df['y'])
    x_line = np.array(ax.get_xlim())
    y_line = intercept + slope * x_line
    ax.plot(x_line, y_line, 'r--', linewidth=2, label='Line of best fit')
    
    return p_value

# Main plotting function (greatly simplified)
def plot_correlation(
    dataset: Dataset, 
    x_key: str, 
    y_key: str,
    color_key: Optional[str] = None,
    test: Optional[str] = "pearson",
    save_path = None,
) -> None:
    """
    Plots an elegant correlation scatter between two parameters in a dataset.

    Args:
        dataset: Dataset object containing the data.
        x_key: Parameter name for the x-axis.
        y_key: Parameter name for the y-axis.
        color_key: Optional parameter name for coloring points.
        test: Optional correlation test ('pearson' or 'spearman').
              If None, no statistical analysis is performed.
    """
    # 1. Prepare and clean data
    clean_df = _prepare_dataframe(dataset, x_key, y_key, color_key)
    
    # 2. Setup the plot
    fig, ax = plt.subplots(figsize=(8, 7), constrained_layout=True)
    
    # 3. Draw the scatter points
    _draw_scatter_points(ax, clean_df, color_key)
    
    # 4. Add statistical analysis and best-fit line
    if test:
        p_value = _add_stats_and_fit_line(ax, clean_df, test)
        if p_value >= 0.05:
            prefix = "NS"
        elif p_value >= 0.01:
            prefix = "A"
        elif p_value >= 0.001:
            prefix = "AA"
        else:
            prefix = "AAA"
    else:
        prefix = "Untested"

    # 5. Finalize plot aesthetics
    ax.set_xlabel(x_key, fontsize=12)
    ax.set_ylabel(y_key, fontsize=12)
    ax.set_title(f"Correlation between {y_key} and {x_key}", fontsize=14)
    ax.grid(True, linestyle='--', alpha=0.5)
    
    if any(ax.get_legend_handles_labels()):
        ax.legend(loc='best')

    if save_path:
        os.makedirs(save_path, exist_ok=True)
        y_str, x_str, color_str = valid_file_string(y_key), valid_file_string(x_key), "color_by_" + valid_file_string(color_key) if color_key else "NoColor"
        plt.savefig(os.path.join(save_path, f"{prefix}_{y_str}_vs_{x_str}_{color_str}.png"), dpi=300)
    else:
        plt.show()
    plt.close(fig)


# Helper for comparison data preparation
def _prepare_comparison_dataframe(dataset: Dataset, x_key: str, y_key: str, color_key: Optional[str]) -> pd.DataFrame:
    """Extracts and cleans data for comparison plots. Treats x as categorical and y as numeric."""
    data: Dict[str, Any] = {
        x_key: dataset.get_param_list(x_key),
        y_key: dataset.get_param_list(y_key),
    }
    if color_key:
        data[color_key] = dataset.get_param_list(color_key)

    df = pd.DataFrame(data)

    # Convert only y to numeric; keep x as categorical
    df[y_key] = pd.to_numeric(df[y_key], errors='coerce')

    # Drop rows where y is NaN
    df.dropna(subset=[y_key], inplace=True)

    return df


# Helper for comparison statistics annotation
def _add_comparison_stats(ax: plt.Axes, df: pd.DataFrame, x_key: str, y_key: str) -> None:
    """Performs t-test for 2 groups or ANOVA for >2 groups and annotates the plot."""
    # Identify groups based on categorical x
    categories = pd.Series(df[x_key]).dropna().unique()
    if len(categories) < 2:
        return

    groups = [pd.Series(df.loc[df[x_key] == cat, y_key]).dropna().values for cat in categories]
    groups = [g for g in groups if len(g) > 0]
    if len(groups) < 2:
        return

    if len(groups) == 2:
        t_stat, p_value = stats.ttest_ind(groups[0], groups[1])
        textstr = f'T-test: t = {t_stat:.2f}\np-value = {p_value:.3f}'
    else:
        f_stat, p_value = stats.f_oneway(*groups)
        textstr = f'ANOVA: F = {f_stat:.2f}\np-value = {p_value:.3f}'

    props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
    ax.text(0.95, 0.95, textstr, transform=ax.transAxes, fontsize=12,
            verticalalignment='top', horizontalalignment='right', bbox=props)


# Main comparison plotting function
def plot_comparison(
        dataset: Dataset,
        x_key: str,
        y_key: str,
        color_key: Optional[str] = None,
        save_path: Optional[str] = None,
) -> None:
    """
    Plots a boxplot with a swarmplot overlay to compare a continuous variable
    across different categories.

    Args:
        dataset: Dataset object containing the data.
        x_key: Categorical parameter name for the x-axis.
        y_key: Numeric parameter name for the y-axis.
        color_key: Optional parameter to group data by hue.
        save_path: Optional directory path to save the plot.
    """
    # 1. Prepare and clean data
    clean_df = _prepare_comparison_dataframe(dataset, x_key, y_key, color_key)

    # 2. Setup the plot
    fig, ax = plt.subplots(figsize=(8, 5), constrained_layout=True)

    # 3. Draw the box and swarm plots
    import seaborn as sns  # Add this import
    sns.boxplot(ax=ax, data=clean_df, x=x_key, y=y_key, hue=color_key,
                boxprops=dict(alpha=0.6))

    # Handle swarmplot differently based on whether hue is provided
    if color_key:
        sns.swarmplot(ax=ax, data=clean_df, x=x_key, y=y_key, hue=color_key,
                      dodge=True, size=3, alpha=0.7)
    else:
        sns.swarmplot(ax=ax, data=clean_df, x=x_key, y=y_key,
                      size=3, color="k", alpha=0.7)

    # 4. Add statistical analysis
    # Perform stats on the whole dataset, not per hue group, for simplicity.
    if color_key:
        # Avoid stats when hue is present to prevent plot clutter.
        pass
    else:
        _add_comparison_stats(ax, clean_df, x_key, y_key)

    # 5. Finalize plot aesthetics (match plot_correlation)
    ax.set_xlabel(x_key, fontsize=12)
    ax.set_ylabel(y_key, fontsize=12)
    ax.set_title(f"Comparison of {y_key} by {x_key}", fontsize=14)
    ax.grid(True, linestyle='--', alpha=0.5)

    if any(ax.get_legend_handles_labels()):
        # Handle duplicate legend entries from box and swarm plots
        handles, labels = ax.get_legend_handles_labels()
        unique_labels: Dict[str, Any] = dict(zip(labels, handles))
        ax.legend(unique_labels.values(), unique_labels.keys(), loc='best')

    # 6. Save or show the plot
    if save_path:
        os.makedirs(save_path, exist_ok=True)
        y_str = valid_file_string(y_key)
        x_str = valid_file_string(x_key)
        color_str = f"hue_by_{valid_file_string(color_key)}" if color_key else "NoHue"
        filename = f"comparison_{y_str}_by_{x_str}_{color_str}.png"
        plt.savefig(os.path.join(save_path, filename), dpi=300)
    else:
        plt.show()

    plt.close(fig)