import warnings

from src.data_core import Dataset
from src.data_loader import load_from_xlsx
from src.data_cleaner import joe_data_cleaner
from src.plotter.basic_plotting import plot_correlation, plot_comparison

# warnings.filterwarnings("ignore")

if __name__ == "__main__":
    all_dataset = []
    for file_name in ("All SAT", "All PSE", "All ACC", "PSEcntl"):
        filepath = rf"C:\Users\<USER>\PycharmProjects\Frankenstein\data\DataFromJoe\{file_name}.xlsx"

        # Load and clean data
        dataset = load_from_xlsx(filepath)
        cleaned_dataset = dataset.apply(joe_data_cleaner)

        print(cleaned_dataset)
        all_dataset.append(cleaned_dataset)

    # Concatenate all datasets
    all_dataset = sum(all_dataset, Dataset())

    # Plot
    print(all_dataset)
    # save_path = "./plots"
    save_path = None
    plot_correlation(all_dataset, 'Depth (μm)', 'qEPSC (pA)(0-3ms Eve', color_key='GrpTrain', save_path=save_path)
    plot_correlation(all_dataset, 'Depth (μm)', 'qEPSC (pA)(0-3ms Eve', color_key='Sex', save_path=save_path)
    plot_comparison(all_dataset, 'Layer (200um)', 'qEPSC (pA)(0-3ms Eve', color_key='Sex', save_path=save_path)
    