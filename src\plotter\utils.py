import re
import os
from typing import Optional

def valid_file_string(s: Optional[str], max_len: int = 20) -> str:
    """
    Convert a string to a valid filename by replacing invalid characters and
    ensuring a safe length.
    """
    if not s:
        return "None"
    
    # Remove leading/trailing whitespace
    s = s.strip()

    # Replace invalid filename characters with an underscore
    # This includes < > : " / \ | ? * and control characters
    s = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '', s)

    # Prevent "File name too long" errors by truncating
    if len(s) > max_len:
        # Truncate while preserving the file extension, if one exists
        name, ext = os.path.splitext(s)
        s = name[:max_len - len(ext)] + ext

    # On Windows, some names are reserved. Add an underscore to avoid issues.
    reserved = {
        'CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4',
        'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3',
        'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    if os.path.splitext(s)[0].upper() in reserved:
        s = '_' + s
        
    # A filename can't be empty or just dots
    if not s or s.strip('.') == '':
        return 'Empty'

    return s