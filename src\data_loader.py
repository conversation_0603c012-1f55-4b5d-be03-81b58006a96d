import pandas as pd
import numpy as np
from pathlib import Path
from src.data_core import Dataset, DataInstance



def load_from_xlsx(filepath: str) -> Dataset:
    """
    Loads data from an Excel file into a Dataset.

    Args:
        filepath: Path to the Excel file (.xlsx or .xls)

    Returns:
        Dataset: A Dataset containing DataInstance objects for each row

    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If the file cannot be read or is empty
        Exception: For other pandas-related errors during file reading
    """
    # Validate file exists
    file_path = Path(filepath)
    if not file_path.exists():
        raise FileNotFoundError(f"Excel file not found: {filepath}")

    try:
        # Read Excel file using pandas
        df = pd.read_excel(filepath)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]

        # Check if DataFrame is empty
        if df.empty:
            raise ValueError(f"Excel file is empty: {filepath}")

        # Convert DataFrame to list of DataInstance objects
        instances = []
        for _, row in df.iterrows():
            # Convert row to dictionary, handling NaN values
            row_dict = {}
            for column, value in row.items():
                # Convert pandas NaN to numpy NaN for consistency with data_core
                if pd.isna(value):
                    row_dict[column] = np.nan
                else:
                    row_dict[column] = value

            # Create DataInstance from row data
            instance = DataInstance(**row_dict)
            instances.append(instance)

        # Create and return Dataset with fix_integrity=True (default)
        # This allows for handling any inconsistencies in the Excel data
        return Dataset(instances, fix_integrity=True)

    except pd.errors.EmptyDataError:
        raise ValueError(f"Excel file contains no data: {filepath}")
    except pd.errors.ParserError as e:
        raise ValueError(f"Error parsing Excel file {filepath}: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error reading Excel file {filepath}: {str(e)}")
